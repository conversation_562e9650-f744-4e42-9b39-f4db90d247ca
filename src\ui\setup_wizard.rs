use anyhow::{Context, Result};
use console::style;
use keyring::Entry;
use tracing::{debug, warn};

use crate::config::{Config, LlmProvider};
use crate::llm::providers::{create_provider, OllamaProvider};
use super::utils::*;

/// Setup wizard for first-time configuration
pub struct SetupWizard;

impl SetupWizard {
    /// Create a new setup wizard
    pub fn new() -> Self {
        Self
    }

    /// Run the setup wizard
    pub async fn run(&mut self) -> Result<Config> {
        print_header("🚀 Welcome to Arien AI Setup");
        println!("Let's configure your AI assistant...\n");

        // Step 1: Choose LLM provider
        let provider = self.choose_provider().await?;

        // Step 2: Configure the chosen provider
        let llm_config = match provider {
            ProviderChoice::OpenAI => self.configure_openai().await?,
            ProviderChoice::Deepseek => self.configure_deepseek().await?,
            ProviderChoice::Ollama => self.configure_ollama().await?,
        };

        // Step 3: Additional configuration
        let config = self.configure_additional_settings(llm_config).await?;

        // Step 4: Test the configuration
        self.test_configuration(&config).await?;

        print_success("Setup completed successfully!");
        Ok(config)
    }

    /// Choose LLM provider
    async fn choose_provider(&self) -> Result<ProviderChoice> {
        print_header("Step 1: Choose your LLM Provider");
        
        let providers = vec![
            "OpenAI (GPT-4, GPT-3.5-turbo) - Requires API key",
            "Deepseek (deepseek-chat, deepseek-reasoner, deepseek-coder) - Requires API key", 
            "Ollama (Local models) - Free, runs locally",
        ];

        let choice = get_choice("Select your preferred LLM provider:", &providers)?;
        
        match choice {
            0 => Ok(ProviderChoice::OpenAI),
            1 => Ok(ProviderChoice::Deepseek),
            2 => Ok(ProviderChoice::Ollama),
            _ => unreachable!(),
        }
    }

    /// Configure OpenAI
    async fn configure_openai(&self) -> Result<LlmProvider> {
        print_header("Configuring OpenAI");
        
        println!("You'll need an OpenAI API key. Get one at: https://platform.openai.com/api-keys");
        
        let api_key = self.get_api_key("OpenAI")?;
        
        let models = vec![
            "gpt-4o (Latest, most capable)",
            "gpt-4o-mini (Fast and efficient)",
            "gpt-4-turbo (Previous generation)",
            "gpt-3.5-turbo (Fastest, cheapest)",
        ];
        
        let model_choice = get_choice("Select your preferred model:", &models)?;
        let model = match model_choice {
            0 => "gpt-4o",
            1 => "gpt-4o-mini", 
            2 => "gpt-4-turbo",
            3 => "gpt-3.5-turbo",
            _ => unreachable!(),
        }.to_string();

        let use_custom_url = get_confirmation("Do you want to use a custom API endpoint?")?;
        let base_url = if use_custom_url {
            Some(get_input("Enter the custom API endpoint URL:")?)
        } else {
            None
        };

        Ok(LlmProvider::OpenAI {
            api_key,
            model,
            base_url,
        })
    }

    /// Configure Deepseek
    async fn configure_deepseek(&self) -> Result<LlmProvider> {
        print_header("Configuring Deepseek");
        
        println!("You'll need a Deepseek API key. Get one at: https://platform.deepseek.com/api-keys");
        
        let api_key = self.get_api_key("Deepseek")?;
        
        let models = vec![
            "deepseek-chat (General purpose)",
            "deepseek-reasoner (Reasoning-focused)",
            "deepseek-coder (Code-focused)",
            "deepseek-math (Math-focused)",
        ];
        
        let model_choice = get_choice("Select your preferred model:", &models)?;
        let model = match model_choice {
            0 => "deepseek-chat",
            1 => "deepseek-reasoner",
            2 => "deepseek-coder",
            _ => unreachable!(),
        }.to_string();

        let use_custom_url = get_confirmation("Do you want to use a custom API endpoint?")?;
        let base_url = if use_custom_url {
            Some(get_input("Enter the custom API endpoint URL:")?)
        } else {
            None
        };

        Ok(LlmProvider::Deepseek {
            api_key,
            model,
            base_url,
        })
    }

    /// Configure Ollama
    async fn configure_ollama(&self) -> Result<LlmProvider> {
        print_header("Configuring Ollama");
        
        println!("Ollama runs AI models locally on your machine.");
        println!("Download and install Ollama from: https://ollama.ai");
        
        let default_url = "http://localhost:11434".to_string();
        let use_default = get_confirmation(&format!("Use default Ollama URL ({})? ", default_url))?;
        
        let base_url = if use_default {
            default_url
        } else {
            get_input("Enter Ollama URL:")?
        };

        // Test Ollama connection
        print_spinner("Testing Ollama connection...");
        let ollama = OllamaProvider::new(base_url.clone())?;
        
        if !ollama.check_availability().await? {
            print_error("Cannot connect to Ollama. Please ensure it's running.");
            return Err(anyhow::anyhow!("Ollama is not available"));
        }

        print_success("Connected to Ollama!");

        // Get available models
        let available_models = ollama.get_models().await
            .context("Failed to get Ollama models")?;

        if available_models.is_empty() {
            print_warning("No models found. You'll need to pull a model first.");
            println!("Example: ollama pull llama2");
            
            let model = get_input("Enter the model name you want to use:")?;
            return Ok(LlmProvider::Ollama { base_url, model });
        }

        println!("\nAvailable models:");
        for (i, model) in available_models.iter().enumerate() {
            println!("  {}. {}", i + 1, model);
        }

        let model_choice = get_choice("Select a model:", &available_models.iter().map(|s| s.as_str()).collect::<Vec<_>>())?;
        let model = available_models[model_choice].clone();

        Ok(LlmProvider::Ollama { base_url, model })
    }

    /// Configure additional settings
    async fn configure_additional_settings(&self, llm: LlmProvider) -> Result<Config> {
        print_header("Additional Configuration");
        
        let mut config = Config::new(llm);

        // Streaming
        let enable_streaming = get_confirmation("Enable streaming responses (recommended)?")?;
        config.streaming = enable_streaming;

        // Safety settings
        let require_confirmation = get_confirmation("Require confirmation for potentially dangerous operations (recommended)?")?;
        config.safety.require_confirmation = require_confirmation;

        // Working directory
        let set_working_dir = get_confirmation("Set a default working directory?")?;
        if set_working_dir {
            let working_dir = get_input("Enter the default working directory path:")?;
            config.working_directory = Some(std::path::PathBuf::from(working_dir));
        }

        // Custom system prompt
        let add_custom_prompt = get_confirmation("Add a custom system prompt?")?;
        if add_custom_prompt {
            let custom_prompt = get_input("Enter your custom system prompt:")?;
            config.custom_system_prompt = Some(custom_prompt);
        }

        Ok(config)
    }

    /// Test the configuration
    async fn test_configuration(&self, config: &Config) -> Result<()> {
        print_header("Testing Configuration");
        
        print_spinner("Creating LLM provider...");
        let provider = create_provider(&config.llm)
            .context("Failed to create LLM provider")?;

        print_success("LLM provider created successfully!");

        print_spinner("Testing connection...");
        let models = provider.get_models().await
            .context("Failed to connect to LLM provider")?;

        print_success(&format!("Connection successful! Found {} models", models.len()));

        if get_confirmation("Test with a simple query?")? {
            print_spinner("Sending test query...");
            
            use crate::llm::{LlmClient, LlmRequestBuilder, conversation::Message};
            
            let client = LlmClient::new(provider);
            let request = LlmRequestBuilder::new(crate::llm::providers::get_default_model(&config.llm))
                .add_message(Message::user("Say 'Hello, Arien AI is working!' and nothing else."))
                .build();

            match client.generate(request).await {
                Ok(response) => {
                    print_success("Test query successful!");
                    println!("Response: {}", style(&response.content).dim());
                }
                Err(error) => {
                    print_warning(&format!("Test query failed: {}", error));
                    println!("This might be due to API limits or network issues.");
                }
            }
        }

        Ok(())
    }

    /// Get API key with secure storage option
    fn get_api_key(&self, provider_name: &str) -> Result<String> {
        let api_key = get_input(&format!("Enter your {} API key:", provider_name))?;
        
        if api_key.is_empty() {
            return Err(anyhow::anyhow!("API key cannot be empty"));
        }

        // Offer to store in keyring
        if get_confirmation("Store API key securely in system keyring?")? {
            match self.store_api_key(provider_name, &api_key) {
                Ok(()) => print_success("API key stored securely"),
                Err(error) => {
                    print_warning(&format!("Failed to store API key: {}", error));
                    println!("The key will still be saved in the configuration file.");
                }
            }
        }

        Ok(api_key)
    }

    /// Store API key in system keyring
    fn store_api_key(&self, provider_name: &str, api_key: &str) -> Result<()> {
        let entry = Entry::new("arien-ai", &format!("{}-api-key", provider_name.to_lowercase()))?;
        entry.set_password(api_key)?;
        debug!("API key stored in keyring for provider: {}", provider_name);
        Ok(())
    }

    /// Load API key from system keyring
    pub fn load_api_key(provider_name: &str) -> Option<String> {
        match Entry::new("arien-ai", &format!("{}-api-key", provider_name.to_lowercase())) {
            Ok(entry) => match entry.get_password() {
                Ok(password) => {
                    debug!("API key loaded from keyring for provider: {}", provider_name);
                    Some(password)
                }
                Err(error) => {
                    debug!("Failed to load API key from keyring: {}", error);
                    None
                }
            },
            Err(error) => {
                warn!("Failed to create keyring entry: {}", error);
                None
            }
        }
    }
}

impl Default for SetupWizard {
    fn default() -> Self {
        Self::new()
    }
}

/// Provider choice enum
#[derive(Debug, Clone, Copy)]
enum ProviderChoice {
    OpenAI,
    Deepseek,
    Ollama,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_setup_wizard_creation() {
        let wizard = SetupWizard::new();
        // Just ensure it can be created without panicking
        assert!(true);
    }

    #[test]
    fn test_api_key_storage() {
        let wizard = SetupWizard::new();
        
        // Test storing and loading (might fail on systems without keyring support)
        if let Ok(()) = wizard.store_api_key("test", "test-key") {
            let loaded = SetupWizard::load_api_key("test");
            assert_eq!(loaded, Some("test-key".to_string()));
        }
    }
}
