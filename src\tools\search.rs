use anyhow::{Context, Result};
use async_trait::async_trait;
use regex::Regex;
use serde::{Deserialize, Serialize};
use schemars::JsonSchema;
use std::path::Path;
use tokio::fs;
use tracing::debug;
use walkdir::WalkDir;

use super::{Tool, ToolCategory, ToolContext, ToolResult, ToolSchema};
use super::schema::{validation, SchemaBuilder};

/// Search files tool
pub struct SearchFilesToolImpl;

impl SearchFilesToolImpl {
    pub fn new() -> Self {
        Self
    }
}

#[derive(Debug, Serialize, Deserialize, JsonSchema)]
struct SearchFilesParams {
    /// Pattern to search for in file names
    pattern: String,
    /// Directory to search in
    path: String,
    /// Use regex for pattern matching
    regex: Option<bool>,
    /// Case sensitive search
    case_sensitive: Option<bool>,
    /// Include hidden files
    include_hidden: Option<bool>,
    /// Search recursively in subdirectories
    recursive: Option<bool>,
    /// File extensions to include (e.g., ["rs", "toml"])
    extensions: Option<Vec<String>>,
    /// Maximum number of results to return
    max_results: Option<usize>,
}

#[async_trait]
impl Tool for SearchFilesToolImpl {
    fn name(&self) -> &str {
        "search_files"
    }

    fn description(&self) -> &str {
        "Search for files by name pattern. Supports regex, case sensitivity, and filtering by extension."
    }

    fn schema(&self) -> ToolSchema {
        SchemaBuilder::new(self.name(), self.description())
            .with_example(serde_json::json!({
                "pattern": "*.rs",
                "path": "./src"
            }))
            .with_example(serde_json::json!({
                "pattern": "test.*\\.py$",
                "path": ".",
                "regex": true,
                "recursive": true
            }))
            .with_example(serde_json::json!({
                "pattern": "config",
                "path": ".",
                "extensions": ["toml", "json", "yaml"]
            }))
            .build::<SearchFilesParams>()
    }

    fn category(&self) -> ToolCategory {
        ToolCategory::Search
    }

    async fn execute(&self, params: serde_json::Value, context: &ToolContext) -> Result<ToolResult> {
        let params: SearchFilesParams = serde_json::from_value(params)
            .context("Failed to parse search_files parameters")?;

        validation::validate_safe_path(&params.path)?;

        let search_path = if Path::new(&params.path).is_absolute() {
            params.path.into()
        } else {
            context.working_dir.join(&params.path)
        };

        debug!("Searching files in: {} with pattern: {}", search_path.display(), params.pattern);

        if !search_path.exists() {
            return Ok(ToolResult::error(format!("Search path does not exist: {}", search_path.display())));
        }

        let use_regex = params.regex.unwrap_or(false);
        let case_sensitive = params.case_sensitive.unwrap_or(true);
        let include_hidden = params.include_hidden.unwrap_or(false);
        let recursive = params.recursive.unwrap_or(true);
        let max_results = params.max_results.unwrap_or(1000);

        let pattern_matcher: Box<dyn Fn(&str) -> bool + Send + Sync> = if use_regex {
            let regex_flags = if case_sensitive {
                regex::RegexBuilder::new(&params.pattern)
            } else {
                regex::RegexBuilder::new(&params.pattern).case_insensitive(true)
            };
            
            let regex = regex_flags.build()
                .with_context(|| format!("Invalid regex pattern: {}", params.pattern))?;
            
            Box::new(move |name: &str| regex.is_match(name))
        } else {
            let pattern = if case_sensitive {
                params.pattern.clone()
            } else {
                params.pattern.to_lowercase()
            };
            
            Box::new(move |name: &str| {
                let name_to_check = if case_sensitive {
                    name.to_string()
                } else {
                    name.to_lowercase()
                };
                
                // Simple glob-like matching
                if pattern.contains('*') {
                    let parts: Vec<&str> = pattern.split('*').collect();
                    if parts.len() == 2 {
                        let (prefix, suffix) = (parts[0], parts[1]);
                        name_to_check.starts_with(prefix) && name_to_check.ends_with(suffix)
                    } else {
                        name_to_check.contains(&pattern.replace('*', ""))
                    }
                } else {
                    name_to_check.contains(&pattern)
                }
            })
        };

        let mut matches = Vec::new();
        let mut total_searched = 0;

        let walker = if recursive {
            WalkDir::new(&search_path)
        } else {
            WalkDir::new(&search_path).max_depth(1)
        };

        for entry in walker {
            if matches.len() >= max_results {
                break;
            }

            let entry = entry.with_context(|| "Failed to read directory entry")?;
            let path = entry.path();
            
            // Skip directories unless we're looking for them specifically
            if path.is_dir() && path != search_path {
                continue;
            }

            total_searched += 1;

            let file_name = match path.file_name().and_then(|n| n.to_str()) {
                Some(name) => name,
                None => continue,
            };

            // Skip hidden files if not requested
            if !include_hidden && file_name.starts_with('.') {
                continue;
            }

            // Check extension filter
            if let Some(ref extensions) = params.extensions {
                let file_ext = path.extension().and_then(|e| e.to_str());
                if !extensions.iter().any(|ext| Some(ext.as_str()) == file_ext) {
                    continue;
                }
            }

            // Check pattern match
            if pattern_matcher(file_name) {
                let relative_path = path.strip_prefix(&search_path)
                    .unwrap_or(path)
                    .display()
                    .to_string();
                matches.push(relative_path);
            }
        }

        let output = if matches.is_empty() {
            "No files found matching the pattern".to_string()
        } else {
            matches.join("\n")
        };

        let metadata = serde_json::json!({
            "search_path": search_path.display().to_string(),
            "pattern": params.pattern,
            "matches_found": matches.len(),
            "total_searched": total_searched,
            "use_regex": use_regex,
            "case_sensitive": case_sensitive,
            "recursive": recursive
        });

        Ok(ToolResult::success(output)
            .with_metadata("search_info".to_string(), metadata))
    }
}

/// Grep tool for searching file contents
pub struct GrepToolImpl;

impl GrepToolImpl {
    pub fn new() -> Self {
        Self
    }
}

#[derive(Debug, Serialize, Deserialize, JsonSchema)]
struct GrepParams {
    /// Pattern to search for in file contents
    pattern: String,
    /// File or directory to search in
    path: String,
    /// Use regex for pattern matching
    regex: Option<bool>,
    /// Case sensitive search
    case_sensitive: Option<bool>,
    /// Show line numbers
    line_numbers: Option<bool>,
    /// Show context lines around matches
    context: Option<usize>,
    /// Search recursively in subdirectories
    recursive: Option<bool>,
    /// File extensions to include
    extensions: Option<Vec<String>>,
    /// Maximum number of matches to return
    max_matches: Option<usize>,
}

#[async_trait]
impl Tool for GrepToolImpl {
    fn name(&self) -> &str {
        "grep"
    }

    fn description(&self) -> &str {
        "Search for text patterns within file contents. Similar to Unix grep command."
    }

    fn schema(&self) -> ToolSchema {
        SchemaBuilder::new(self.name(), self.description())
            .with_example(serde_json::json!({
                "pattern": "TODO",
                "path": "./src",
                "recursive": true
            }))
            .with_example(serde_json::json!({
                "pattern": "fn\\s+\\w+",
                "path": "main.rs",
                "regex": true,
                "line_numbers": true
            }))
            .with_example(serde_json::json!({
                "pattern": "error",
                "path": ".",
                "context": 2,
                "extensions": ["rs", "toml"]
            }))
            .build::<GrepParams>()
    }

    fn category(&self) -> ToolCategory {
        ToolCategory::Search
    }

    async fn execute(&self, params: serde_json::Value, context: &ToolContext) -> Result<ToolResult> {
        let params: GrepParams = serde_json::from_value(params)
            .context("Failed to parse grep parameters")?;

        validation::validate_safe_path(&params.path)?;

        let search_path = if Path::new(&params.path).is_absolute() {
            params.path.into()
        } else {
            context.working_dir.join(&params.path)
        };

        debug!("Grepping in: {} for pattern: {}", search_path.display(), params.pattern);

        if !search_path.exists() {
            return Ok(ToolResult::error(format!("Search path does not exist: {}", search_path.display())));
        }

        let use_regex = params.regex.unwrap_or(false);
        let case_sensitive = params.case_sensitive.unwrap_or(true);
        let show_line_numbers = params.line_numbers.unwrap_or(false);
        let context_lines = params.context.unwrap_or(0);
        let recursive = params.recursive.unwrap_or(true);
        let max_matches = params.max_matches.unwrap_or(1000);

        let regex = if use_regex {
            let builder = if case_sensitive {
                regex::RegexBuilder::new(&params.pattern)
            } else {
                regex::RegexBuilder::new(&params.pattern).case_insensitive(true)
            };
            
            Some(builder.build()
                .with_context(|| format!("Invalid regex pattern: {}", params.pattern))?)
        } else {
            None
        };

        let mut matches = Vec::new();
        let mut files_searched = 0;
        let mut total_matches = 0;

        let files_to_search: Vec<std::path::PathBuf> = if search_path.is_file() {
            vec![search_path]
        } else {
            let walker = if recursive {
                WalkDir::new(&search_path)
            } else {
                WalkDir::new(&search_path).max_depth(1)
            };

            walker
                .into_iter()
                .filter_map(|entry| entry.ok())
                .filter(|entry| entry.path().is_file())
                .filter(|entry| {
                    if let Some(ref extensions) = params.extensions {
                        let file_ext = entry.path().extension().and_then(|e| e.to_str());
                        extensions.iter().any(|ext| Some(ext.as_str()) == file_ext)
                    } else {
                        true
                    }
                })
                .map(|entry| entry.path().to_path_buf())
                .collect()
        };

        for file_path in files_to_search {
            if total_matches >= max_matches {
                break;
            }

            files_searched += 1;

            let content = match fs::read_to_string(&file_path).await {
                Ok(content) => content,
                Err(_) => continue, // Skip binary files or files we can't read
            };

            let lines: Vec<&str> = content.lines().collect();
            let relative_path = file_path.strip_prefix(&search_path)
                .unwrap_or(&file_path)
                .display()
                .to_string();

            for (line_num, line) in lines.iter().enumerate() {
                if total_matches >= max_matches {
                    break;
                }

                let is_match = if let Some(ref regex) = regex {
                    regex.is_match(line)
                } else {
                    let line_to_check = if case_sensitive {
                        line.to_string()
                    } else {
                        line.to_lowercase()
                    };
                    let pattern_to_check = if case_sensitive {
                        params.pattern.clone()
                    } else {
                        params.pattern.to_lowercase()
                    };
                    line_to_check.contains(&pattern_to_check)
                };

                if is_match {
                    total_matches += 1;

                    let mut match_lines = Vec::new();
                    
                    // Add context before
                    let start = if line_num >= context_lines {
                        line_num - context_lines
                    } else {
                        0
                    };
                    
                    // Add context after
                    let end = std::cmp::min(line_num + context_lines + 1, lines.len());
                    
                    for i in start..end {
                        let prefix = if show_line_numbers {
                            if i == line_num {
                                format!("{}:{}:", relative_path, i + 1)
                            } else {
                                format!("{}:{}:", relative_path, i + 1)
                            }
                        } else {
                            if context_lines > 0 && i != line_num {
                                format!("{}:", relative_path)
                            } else {
                                format!("{}:", relative_path)
                            }
                        };
                        
                        match_lines.push(format!("{} {}", prefix, lines[i]));
                    }
                    
                    matches.push(match_lines.join("\n"));
                }
            }
        }

        let output = if matches.is_empty() {
            "No matches found".to_string()
        } else {
            matches.join("\n--\n")
        };

        let metadata = serde_json::json!({
            "search_path": search_path.display().to_string(),
            "pattern": params.pattern,
            "files_searched": files_searched,
            "total_matches": total_matches,
            "use_regex": use_regex,
            "case_sensitive": case_sensitive,
            "recursive": recursive
        });

        Ok(ToolResult::success(output)
            .with_metadata("grep_info".to_string(), metadata))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use tokio::fs;

    #[tokio::test]
    async fn test_search_files() -> Result<()> {
        let temp_dir = TempDir::new()?;
        let temp_path = temp_dir.path();

        // Create test files
        fs::write(temp_path.join("test.rs"), "fn main() {}").await?;
        fs::write(temp_path.join("config.toml"), "[package]").await?;
        fs::write(temp_path.join("README.md"), "# Project").await?;

        let tool = SearchFilesToolImpl::new();
        let context = ToolContext {
            working_dir: temp_path.to_path_buf(),
            ..Default::default()
        };

        // Test simple pattern search
        let params = serde_json::json!({
            "pattern": "*.rs",
            "path": "."
        });

        let result = tool.execute(params, &context).await?;
        assert!(result.success);
        assert!(result.output.contains("test.rs"));

        Ok(())
    }

    #[tokio::test]
    async fn test_grep() -> Result<()> {
        let temp_dir = TempDir::new()?;
        let temp_path = temp_dir.path();

        // Create test file with content
        fs::write(temp_path.join("test.rs"), 
            "fn main() {\n    println!(\"Hello, world!\");\n    // TODO: add more features\n}").await?;

        let tool = GrepToolImpl::new();
        let context = ToolContext {
            working_dir: temp_path.to_path_buf(),
            ..Default::default()
        };

        // Test grep for TODO
        let params = serde_json::json!({
            "pattern": "TODO",
            "path": "test.rs",
            "line_numbers": true
        });

        let result = tool.execute(params, &context).await?;
        assert!(result.success);
        assert!(result.output.contains("TODO"));

        Ok(())
    }
}
