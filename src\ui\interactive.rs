use anyhow::{Context, Result};
use console::{style, Term};
use std::io::{self, Write};
use tokio::time::{sleep, Duration};
use tracing::{debug, info};

use crate::agent::AgentOrchestrator;
use crate::config::Config;
use crate::llm::conversation::{Conversation, ConversationManager};
use super::utils::*;

/// Interactive mode for conversational AI interaction
pub struct InteractiveMode {
    orchestrator: AgentOrchestrator,
    conversation_manager: ConversationManager,
    current_conversation_id: Option<uuid::Uuid>,
    config: Config,
    term: Term,
}

impl InteractiveMode {
    /// Create a new interactive mode
    pub async fn new(config: Config) -> Result<Self> {
        let orchestrator = AgentOrchestrator::new(config.clone()).await?;
        let conversation_manager = ConversationManager::new();
        let term = Term::stdout();

        Ok(Self {
            orchestrator,
            conversation_manager,
            current_conversation_id: None,
            config,
            term,
        })
    }

    /// Run the interactive mode
    pub async fn run(&mut self) -> Result<()> {
        self.show_welcome().await?;
        self.start_new_conversation().await?;

        loop {
            match self.handle_user_input().await {
                Ok(should_continue) => {
                    if !should_continue {
                        break;
                    }
                }
                Err(error) => {
                    print_error(&format!("Error: {}", error));
                    if !get_confirmation("Continue?")? {
                        break;
                    }
                }
            }
        }

        self.show_goodbye().await?;
        Ok(())
    }

    /// Show welcome message
    async fn show_welcome(&self) -> Result<()> {
        self.term.clear_screen()?;
        
        println!("{}", style("🤖 Arien AI - Interactive Mode").bold().cyan());
        println!("{}", style("═".repeat(50)).dim());
        println!();
        
        print_info(&format!("Provider: {}", self.config.llm.name()));
        print_info(&format!("Model: {}", self.config.llm.model()));
        print_info(&format!("Streaming: {}", if self.config.streaming { "Enabled" } else { "Disabled" }));
        
        println!();
        println!("{}", style("Commands:").bold());
        println!("  /help     - Show help");
        println!("  /new      - Start new conversation");
        println!("  /list     - List conversations");
        println!("  /switch   - Switch conversation");
        println!("  /clear    - Clear current conversation");
        println!("  /config   - Show configuration");
        println!("  /tools    - List available tools");
        println!("  /exit     - Exit interactive mode");
        println!();
        
        Ok(())
    }

    /// Show goodbye message
    async fn show_goodbye(&self) -> Result<()> {
        println!();
        print_success("Thank you for using Arien AI! 👋");
        
        if self.conversation_manager.conversation_count() > 0 {
            print_info("Your conversations have been saved.");
        }
        
        Ok(())
    }

    /// Start a new conversation
    async fn start_new_conversation(&mut self) -> Result<()> {
        let title = format!("Conversation {}", chrono::Utc::now().format("%Y-%m-%d %H:%M"));
        let conversation_id = self.conversation_manager.create_conversation(title);
        self.current_conversation_id = Some(conversation_id);
        
        print_success("Started new conversation");
        Ok(())
    }

    /// Handle user input
    async fn handle_user_input(&mut self) -> Result<bool> {
        print!("\n{} ", style("You:").bold().green());
        io::stdout().flush()?;
        
        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        let input = input.trim();
        
        if input.is_empty() {
            return Ok(true);
        }

        // Handle commands
        if input.starts_with('/') {
            return self.handle_command(input).await;
        }

        // Handle regular conversation
        self.handle_conversation(input).await?;
        Ok(true)
    }

    /// Handle slash commands
    async fn handle_command(&mut self, command: &str) -> Result<bool> {
        let parts: Vec<&str> = command.split_whitespace().collect();
        let cmd = parts[0];

        match cmd {
            "/help" => {
                self.show_help().await?;
            }
            "/new" => {
                self.start_new_conversation().await?;
            }
            "/list" => {
                self.list_conversations().await?;
            }
            "/switch" => {
                self.switch_conversation().await?;
            }
            "/clear" => {
                self.clear_conversation().await?;
            }
            "/config" => {
                self.show_config().await?;
            }
            "/tools" => {
                self.show_tools().await?;
            }
            "/exit" | "/quit" => {
                return Ok(false);
            }
            _ => {
                print_warning(&format!("Unknown command: {}. Type /help for available commands.", cmd));
            }
        }

        Ok(true)
    }

    /// Handle conversation input
    async fn handle_conversation(&mut self, input: &str) -> Result<()> {
        // Add user message to conversation
        if let Some(conversation) = self.get_current_conversation_mut() {
            conversation.add_user_message(input);
        }

        // Show AI thinking indicator
        print!("\n{} ", style("Arien:").bold().blue());
        io::stdout().flush()?;

        if self.config.streaming {
            // Handle streaming response
            let response = self.orchestrator.process_message_stream(input).await?;
            
            // Add assistant response to conversation
            if let Some(conversation) = self.get_current_conversation_mut() {
                conversation.add_assistant_message(&response.content);
            }
        } else {
            // Handle non-streaming response
            print_spinner("Thinking...");
            let response = self.orchestrator.process_message(input).await?;
            
            // Clear spinner and show response
            clear_line();
            move_cursor_up(1);
            print!("{} {}", style("Arien:").bold().blue(), response.content);
            
            // Add assistant response to conversation
            if let Some(conversation) = self.get_current_conversation_mut() {
                conversation.add_assistant_message(&response.content);
            }
        }

        Ok(())
    }

    /// Show help
    async fn show_help(&self) -> Result<()> {
        print_header("Help - Available Commands");
        
        let mut table = Table::new(vec!["Command".to_string(), "Description".to_string()]);
        table.add_row(vec!["/help".to_string(), "Show this help message".to_string()]);
        table.add_row(vec!["/new".to_string(), "Start a new conversation".to_string()]);
        table.add_row(vec!["/list".to_string(), "List all conversations".to_string()]);
        table.add_row(vec!["/switch".to_string(), "Switch to a different conversation".to_string()]);
        table.add_row(vec!["/clear".to_string(), "Clear the current conversation".to_string()]);
        table.add_row(vec!["/config".to_string(), "Show current configuration".to_string()]);
        table.add_row(vec!["/tools".to_string(), "List available tools".to_string()]);
        table.add_row(vec!["/exit".to_string(), "Exit interactive mode".to_string()]);
        
        table.display();
        
        println!("\n{}", style("Tips:").bold());
        println!("• Just type your message to chat with the AI");
        println!("• The AI can use tools to help with tasks");
        println!("• Use natural language to describe what you want to do");
        
        Ok(())
    }

    /// List conversations
    async fn list_conversations(&self) -> Result<()> {
        let conversations = self.conversation_manager.list_conversations();
        
        if conversations.is_empty() {
            print_info("No conversations found.");
            return Ok(());
        }

        print_header("Conversations");
        
        let mut table = Table::new(vec![
            "ID".to_string(),
            "Title".to_string(),
            "Messages".to_string(),
            "Created".to_string(),
        ]);

        for conv in conversations {
            let id_short = conv.id.to_string()[..8].to_string();
            let created = conv.created_at.format("%Y-%m-%d %H:%M").to_string();
            
            table.add_row(vec![
                id_short,
                conv.title,
                conv.total_messages.to_string(),
                created,
            ]);
        }

        table.display();
        Ok(())
    }

    /// Switch conversation
    async fn switch_conversation(&mut self) -> Result<()> {
        let conversations = self.conversation_manager.list_conversations();
        
        if conversations.is_empty() {
            print_info("No conversations to switch to.");
            return Ok(());
        }

        println!("\nAvailable conversations:");
        for (i, conv) in conversations.iter().enumerate() {
            let id_short = conv.id.to_string()[..8].to_string();
            println!("  {}. {} ({})", i + 1, conv.title, id_short);
        }

        let choice = get_choice("Select conversation:", 
            &conversations.iter().map(|c| c.title.as_str()).collect::<Vec<_>>())?;
        
        let selected_id = conversations[choice].id;
        self.conversation_manager.set_current_conversation(selected_id)?;
        self.current_conversation_id = Some(selected_id);
        
        print_success(&format!("Switched to conversation: {}", conversations[choice].title));
        Ok(())
    }

    /// Clear current conversation
    async fn clear_conversation(&mut self) -> Result<()> {
        if let Some(conversation) = self.get_current_conversation_mut() {
            if get_confirmation("Clear all messages in the current conversation?")? {
                conversation.clear_except_system();
                print_success("Conversation cleared");
            }
        } else {
            print_warning("No active conversation to clear");
        }
        Ok(())
    }

    /// Show configuration
    async fn show_config(&self) -> Result<()> {
        print_header("Current Configuration");
        
        let mut table = Table::new(vec!["Setting".to_string(), "Value".to_string()]);
        table.add_row(vec!["Provider".to_string(), self.config.llm.name().to_string()]);
        table.add_row(vec!["Model".to_string(), self.config.llm.model().to_string()]);
        table.add_row(vec!["Streaming".to_string(), self.config.streaming.to_string()]);
        table.add_row(vec!["Safety Confirmation".to_string(), self.config.safety.require_confirmation.to_string()]);
        table.add_row(vec!["Max History".to_string(), self.config.max_history.to_string()]);
        table.add_row(vec!["Auto Save".to_string(), self.config.auto_save.to_string()]);
        
        if let Some(ref working_dir) = self.config.working_directory {
            table.add_row(vec!["Working Directory".to_string(), working_dir.display().to_string()]);
        }
        
        table.display();
        Ok(())
    }

    /// Show available tools
    async fn show_tools(&self) -> Result<()> {
        let tools = self.orchestrator.get_available_tools().await?;
        
        print_header("Available Tools");
        
        let mut table = Table::new(vec![
            "Name".to_string(),
            "Category".to_string(),
            "Description".to_string(),
            "Safe".to_string(),
        ]);

        for tool in tools {
            table.add_row(vec![
                tool.name,
                tool.category.to_string(),
                tool.description,
                if tool.is_safe { "Yes" } else { "No" }.to_string(),
            ]);
        }

        table.display();
        Ok(())
    }

    /// Get current conversation
    fn get_current_conversation(&self) -> Option<&Conversation> {
        self.current_conversation_id
            .and_then(|id| self.conversation_manager.get_conversation(id))
    }

    /// Get current conversation mutably
    fn get_current_conversation_mut(&mut self) -> Option<&mut Conversation> {
        self.current_conversation_id
            .and_then(|id| self.conversation_manager.get_conversation_mut(id))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::LlmProvider;

    #[tokio::test]
    async fn test_interactive_mode_creation() -> Result<()> {
        let config = Config::new(LlmProvider::Ollama {
            base_url: "http://localhost:11434".to_string(),
            model: "test".to_string(),
        });

        // This might fail if dependencies aren't available, but we're testing the structure
        match InteractiveMode::new(config).await {
            Ok(_) => {
                // Success case
                assert!(true);
            }
            Err(_) => {
                // Expected to fail in test environment without actual LLM providers
                assert!(true);
            }
        }

        Ok(())
    }
}
