use anyhow::{Context, Result};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{debug, info, warn};

use super::{Tool, ToolCategory, ToolContext, ToolResult, ToolSchema};

/// Thread-safe tool registry that manages all available tools
#[derive(Debug, Clone)]
pub struct ToolRegistry {
    tools: HashMap<String, Arc<dyn Tool>>,
    categories: HashMap<ToolCategory, Vec<String>>,
}

impl ToolRegistry {
    /// Create a new empty tool registry
    pub fn new() -> Self {
        Self {
            tools: HashMap::new(),
            categories: HashMap::new(),
        }
    }

    /// Register a new tool
    pub fn register<T: Tool + 'static>(&mut self, tool: T) -> Result<()> {
        let name = tool.name().to_string();
        let category = tool.category();
        
        if self.tools.contains_key(&name) {
            anyhow::bail!("Tool '{}' is already registered", name);
        }
        
        debug!("Registering tool: {} (category: {})", name, category);
        
        // Add to tools map
        self.tools.insert(name.clone(), Arc::new(tool));
        
        // Add to category index
        self.categories
            .entry(category)
            .or_insert_with(Vec::new)
            .push(name);
        
        info!("Tool '{}' registered successfully", name);
        Ok(())
    }

    /// Get a tool by name
    pub fn get_tool(&self, name: &str) -> Option<Arc<dyn Tool>> {
        self.tools.get(name).cloned()
    }

    /// Get all tool names
    pub fn tool_names(&self) -> Vec<String> {
        self.tools.keys().cloned().collect()
    }

    /// Get tools by category
    pub fn tools_by_category(&self, category: ToolCategory) -> Vec<Arc<dyn Tool>> {
        self.categories
            .get(&category)
            .map(|names| {
                names
                    .iter()
                    .filter_map(|name| self.tools.get(name).cloned())
                    .collect()
            })
            .unwrap_or_default()
    }

    /// Get all categories with tool counts
    pub fn categories(&self) -> HashMap<ToolCategory, usize> {
        self.categories
            .iter()
            .map(|(category, tools)| (*category, tools.len()))
            .collect()
    }

    /// Get tool schemas for LLM function calling
    pub fn get_schemas(&self) -> Vec<ToolSchema> {
        self.tools
            .values()
            .map(|tool| tool.schema())
            .collect()
    }

    /// Get schemas for a specific category
    pub fn get_schemas_by_category(&self, category: ToolCategory) -> Vec<ToolSchema> {
        self.tools_by_category(category)
            .iter()
            .map(|tool| tool.schema())
            .collect()
    }

    /// Execute a tool by name with parameters
    pub async fn execute_tool(
        &self,
        name: &str,
        params: serde_json::Value,
        context: &ToolContext,
    ) -> Result<ToolResult> {
        let tool = self.get_tool(name)
            .with_context(|| format!("Tool '{}' not found", name))?;
        
        debug!("Executing tool: {} with params: {}", name, params);
        
        // Validate parameters against schema
        let schema = tool.schema();
        schema.validate(&params)
            .with_context(|| format!("Parameter validation failed for tool '{}'", name))?;
        
        // Check if confirmation is required
        if tool.requires_confirmation() && context.safety_enabled {
            if let Some(confirm_fn) = context.confirm_callback {
                let message = format!(
                    "Tool '{}' requires confirmation. Description: {}\nParameters: {}\nProceed?",
                    name, tool.description(), params
                );
                
                if !confirm_fn(&message) {
                    return Ok(ToolResult::error("Operation cancelled by user".to_string()));
                }
            } else {
                warn!("Tool '{}' requires confirmation but no callback provided", name);
                return Ok(ToolResult::error(
                    "Tool requires confirmation but no confirmation mechanism available".to_string()
                ));
            }
        }
        
        // Execute the tool
        let start_time = std::time::Instant::now();
        let result = tool.execute(params, context).await;
        let execution_time = start_time.elapsed();
        
        match result {
            Ok(mut tool_result) => {
                tool_result = tool_result.with_metadata(
                    "execution_time_ms".to_string(),
                    serde_json::json!(execution_time.as_millis()),
                );
                
                debug!("Tool '{}' executed successfully in {:?}", name, execution_time);
                Ok(tool_result)
            }
            Err(error) => {
                warn!("Tool '{}' execution failed: {}", name, error);
                Ok(ToolResult::error(format!("Tool execution failed: {}", error)))
            }
        }
    }

    /// Check if a tool exists
    pub fn has_tool(&self, name: &str) -> bool {
        self.tools.contains_key(name)
    }

    /// Get tool count
    pub fn tool_count(&self) -> usize {
        self.tools.len()
    }

    /// Get tool information for display
    pub fn get_tool_info(&self, name: &str) -> Option<ToolInfo> {
        self.get_tool(name).map(|tool| ToolInfo {
            name: tool.name().to_string(),
            description: tool.description().to_string(),
            category: tool.category(),
            requires_confirmation: tool.requires_confirmation(),
            is_safe: tool.is_safe(),
            schema: tool.schema(),
        })
    }

    /// Get all tool information
    pub fn get_all_tool_info(&self) -> Vec<ToolInfo> {
        self.tools
            .keys()
            .filter_map(|name| self.get_tool_info(name))
            .collect()
    }
}

impl Default for ToolRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// Tool information for display and documentation
#[derive(Debug, Clone)]
pub struct ToolInfo {
    pub name: String,
    pub description: String,
    pub category: ToolCategory,
    pub requires_confirmation: bool,
    pub is_safe: bool,
    pub schema: ToolSchema,
}

impl ToolInfo {
    /// Format tool info for display
    pub fn format_info(&self) -> String {
        format!(
            "🔧 {} ({})\n  {}\n  Safety: {} | Confirmation: {}",
            self.name,
            self.category,
            self.description,
            if self.is_safe { "Safe" } else { "Unsafe" },
            if self.requires_confirmation { "Required" } else { "Not Required" }
        )
    }
}

/// Builder for creating and configuring a tool registry
pub struct ToolRegistryBuilder {
    registry: ToolRegistry,
}

impl ToolRegistryBuilder {
    pub fn new() -> Self {
        Self {
            registry: ToolRegistry::new(),
        }
    }

    pub fn with_tool<T: Tool + 'static>(mut self, tool: T) -> Result<Self> {
        self.registry.register(tool)?;
        Ok(self)
    }

    pub fn with_default_tools(mut self) -> Result<Self> {
        // Register default tools
        use super::{file_ops::*, shell_ops::*, search::*};
        
        self.registry.register(ReadFileToolImpl::new())?;
        self.registry.register(WriteFileToolImpl::new())?;
        self.registry.register(ListDirectoryToolImpl::new())?;
        self.registry.register(CreateDirectoryToolImpl::new())?;
        self.registry.register(DeleteFileToolImpl::new())?;
        
        self.registry.register(ExecuteCommandToolImpl::new())?;
        self.registry.register(WhichCommandToolImpl::new())?;
        
        self.registry.register(SearchFilesToolImpl::new())?;
        self.registry.register(GrepToolImpl::new())?;
        
        Ok(self)
    }

    pub fn build(self) -> ToolRegistry {
        info!("Tool registry built with {} tools", self.registry.tool_count());
        self.registry
    }
}

impl Default for ToolRegistryBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;

    struct MockTool {
        name: String,
        description: String,
    }

    #[async_trait]
    impl Tool for MockTool {
        fn name(&self) -> &str {
            &self.name
        }

        fn description(&self) -> &str {
            &self.description
        }

        fn schema(&self) -> ToolSchema {
            use super::super::schema::*;
            ToolSchema::new::<FilePathParams>(
                self.name.clone(),
                self.description.clone(),
                false,
            )
        }

        async fn execute(
            &self,
            _params: serde_json::Value,
            _context: &ToolContext,
        ) -> Result<ToolResult> {
            Ok(ToolResult::success("Mock execution".to_string()))
        }
    }

    #[tokio::test]
    async fn test_tool_registry() -> Result<()> {
        let mut registry = ToolRegistry::new();
        
        let tool = MockTool {
            name: "test_tool".to_string(),
            description: "A test tool".to_string(),
        };
        
        registry.register(tool)?;
        
        assert!(registry.has_tool("test_tool"));
        assert_eq!(registry.tool_count(), 1);
        
        let context = ToolContext::default();
        let result = registry.execute_tool(
            "test_tool",
            serde_json::json!({"path": "test"}),
            &context,
        ).await?;
        
        assert!(result.success);
        assert_eq!(result.output, "Mock execution");
        
        Ok(())
    }

    #[test]
    fn test_tool_registry_builder() -> Result<()> {
        let registry = ToolRegistryBuilder::new()
            .with_tool(MockTool {
                name: "tool1".to_string(),
                description: "Tool 1".to_string(),
            })?
            .with_tool(MockTool {
                name: "tool2".to_string(),
                description: "Tool 2".to_string(),
            })?
            .build();
        
        assert_eq!(registry.tool_count(), 2);
        assert!(registry.has_tool("tool1"));
        assert!(registry.has_tool("tool2"));
        
        Ok(())
    }
}
